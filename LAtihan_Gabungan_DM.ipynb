{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "source": ["!pip install tabulate\n", "\n", "import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.naive_bayes import GaussianNB\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.metrics import (\n", "    classification_report, accuracy_score, precision_score, recall_score, f1_score,\n", "    ConfusionMatrixDisplay\n", ")\n", "from tabulate import tabulate\n", "\n", "# ==== 1. LOAD DATASET ====\n", "df = pd.read_excel('dataset_paylater_mahasiswa.xlsx')\n", "\n", "# ==== 2. ENCODING ====\n", "le_fakultas = LabelEncoder()\n", "le_gender = LabelEncoder()\n", "le_kategori = LabelEncoder()\n", "\n", "df['Fakultas_enc'] = le_fakultas.fit_transform(df['Fakultas'])\n", "df['Gender_enc'] = le_gender.fit_transform(df['<PERSON><PERSON>'])\n", "df['KategoriGayaHidup_enc'] = le_kategori.fit_transform(df['<PERSON><PERSON><PERSON>du<PERSON>'])\n", "\n", "# ==== 3. FITUR & TARGET ====\n", "X = df[['Fakultas_enc', 'Gender_enc', 'Frekuensi Penggunaan Paylater']]\n", "y = df['KategoriGayaHidup_enc']\n", "\n", "# ==== 4. SPLIT DATA ====\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# ==== 5. MODEL TERBAIK ====\n", "nb_model = GaussianNB()\n", "nb_model.fit(X_train, y_train)\n", "nb_preds = nb_model.predict(X_test)\n", "\n", "dt_model = DecisionTreeClassifier(random_state=42)\n", "dt_model.fit(X_train, y_train)\n", "dt_preds = dt_model.predict(X_test)\n", "\n", "nb_f1 = f1_score(y_test, nb_preds, average='macro', zero_division=0)\n", "dt_f1 = f1_score(y_test, dt_preds, average='macro', zero_division=0)\n", "\n", "if dt_f1 >= nb_f1:\n", "    best_model = dt_model\n", "    model_name = \"Decision Tree\"\n", "else:\n", "    best_model = nb_model\n", "    model_name = \"<PERSON><PERSON>es\"\n", "\n", "print(f\"\\n>>> Model terbaik berdasarkan F1-score adalah: {model_name}\")\n", "\n", "# ==== 6. PREDIKSI KE SEMUA DATA ====\n", "df['Predicted_<PERSON><PERSON><PERSON>'] = best_model.predict(X)\n", "df['Predicted_Kategori_Label'] = le_kategori.inverse_transform(df['Predicted_Kategori'])\n", "\n", "# ==== 7. KATEGORI BELANJA ====\n", "if '<PERSON><PERSON><PERSON>' in df.columns:\n", "    df['<PERSON><PERSON><PERSON>_<PERSON>'] = df['<PERSON><PERSON><PERSON>'].apply(lambda x: 'Jarang' if x < 2000000 else 'Sering')\n", "else:\n", "    df['<PERSON><PERSON><PERSON>_<PERSON>'] = 'Unknown'\n", "\n", "# ==== 8. AGGREGASI SEMUA FITUR ====\n", "fitur_rekap = [\n", "    ('<PERSON><PERSON>', ['<PERSON><PERSON>-la<PERSON>', '<PERSON><PERSON><PERSON><PERSON>']),\n", "    ('<PERSON><PERSON><PERSON>_<PERSON>', ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>']),\n", "    ('<PERSON><PERSON><PERSON><PERSON>', ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Pertanian', 'Sastra', 'Teknik']),\n", "    ('Status Tempat Tinggal', ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'])\n", "]\n", "\n", "pendapatan_cols = ['<PERSON><PERSON><PERSON>', 'Part Time', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>']\n", "gaya_hidup_cols = ['Ren<PERSON>', 'Sedang', 'Tinggi']\n", "\n", "all_rows = []\n", "for fitur, vals in fitur_rekap:\n", "    for v in vals:\n", "        row = [fitur, v]\n", "        for kategori in gaya_hidup_cols:\n", "            for pendapatan in pendapatan_cols:\n", "                n = len(df[\n", "                    (df[fitur] == v) &\n", "                    (df['Predicted_Kategori_Label'] == kategori) &\n", "                    (df['Sumber Pendapatan'] == pendapatan)\n", "                ])\n", "                row.append(n)\n", "        all_rows.append(row)\n", "\n", "# ==== 9. HEADER TABEL ====\n", "header_super = [\"<PERSON><PERSON>\", \"<PERSON><PERSON>\"]\n", "for gh in gaya_hidup_cols:\n", "    header_super += [gh]*4\n", "header_detail = [\"\", \"\"] + pendapatan_cols*3\n", "\n", "# Gabungkan header dan data\n", "table = [header_super, header_detail] + all_rows\n", "\n", "print(\"\\n=== TABEL REKAP SEMUA FITUR ===\")\n", "print(tabulate(table, headers=\"firstrow\", tablefmt=\"grid\", stralign=\"center\", numalign=\"center\"))\n", "\n", "# ==== 10. (Optional) EKSPOR KE EXCEL ====\n", "header_excel = [\"<PERSON><PERSON>\", \"<PERSON><PERSON>\"] + [f\"{g}-{p}\" for g in gaya_hidup_cols for p in pendapatan_cols]\n", "pd.DataFrame(all_rows, columns=header_excel).to_excel(\"rekap_gayahidup_allfitur.xlsx\", index=False)\n", "print(\"\\nTabel rekap berhasil diekspor ke: rekap_gayahidup_allfitur.xlsx\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kEXvzllC_XH9", "outputId": "94d0c3b1-ce10-4e20-d85a-c89b8bc9ffc9"}, "execution_count": 11, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: tabulate in /usr/local/lib/python3.11/dist-packages (0.9.0)\n", "\n", ">>> Model terb<PERSON><PERSON> F1-score adalah: Decision Tree\n", "\n", "=== TABEL REKAP SEMUA FITUR ===\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "|         Fitur         |   Nilai Fitur   |  Rendah  |  Rendah   |  Rendah   |    Rendah     |  Sedang  |  Sedang   |  Sedang   |    Sedang     |  Tinggi  |  Tinggi   |  Tinggi   |    Tinggi     |\n", "+=======================+=================+==========+===========+===========+===============+==========+===========+===========+===============+==========+===========+===========+===============+\n", "|                       |                 | Beasiswa | Part Time | Orang Tua | Usaha Sendiri | Beasiswa | Part Time | Orang Tua | Usaha Sendiri | Beasiswa | Part Time | Orang Tua | Usaha Sendiri |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "|     <PERSON><PERSON>     |    <PERSON><PERSON><PERSON><PERSON><PERSON>    |   582    |     0     |    576    |      616      |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "|     <PERSON><PERSON>     |    Perempuan    |   581    |     0     |    534    |      553      |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "|   Belanja_Kategori    |     Jarang      |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "|   Belanja_Kategori    |     Sering      |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "|       Fakultas        |     Ekonomi     |   190    |     0     |    181    |      192      |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "|       Fakultas        |      Hukum      |   191    |     0     |    187    |      194      |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "|       Fakultas        |   Kedokteran    |   193    |     0     |    186    |      163      |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "|       Fakultas        |    Pertanian    |   211    |     0     |    187    |      219      |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "|       Fakultas        |     Sastra      |   195    |     0     |    175    |      213      |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "|       Fakultas        |     Teknik      |   183    |     0     |    194    |      188      |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "| Status Tempat Tinggal |     Asrama      |   360    |     0     |    378    |      386      |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "| Status Tempat Tinggal |       Kos       |   380    |     0     |    370    |      394      |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "| Status Tempat Tinggal | <PERSON><PERSON>h <PERSON> |   423    |     0     |    362    |      389      |    0     |     0     |     0     |       0       |    0     |     0     |     0     |       0       |\n", "+-----------------------+-----------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+----------+-----------+-----------+---------------+\n", "\n", "Tabel rekap berhasil diekspor ke: rekap_gayahidup_allfitur.xlsx\n"]}]}, {"cell_type": "code", "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# Dataframe rekap (harus sudah ada!)\n", "# rekap_df: kolom = [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>]\n", "# <PERSON><PERSON> belum ada:\n", "# rekap_df = pd.DataFrame(rekap, columns=[\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\"] + pendapatan_cols)\n", "\n", "# --- Siapkan Data ---\n", "labels = []\n", "male_counts = {p: [] for p in pendapatan_cols}\n", "female_counts = {p: [] for p in pendapatan_cols}\n", "\n", "for fakultas in fakultas_list:\n", "    labels.append(fakultas + \"\\nL\")\n", "    labels.append(fakultas + \"\\nP\")\n", "    # Laki-laki\n", "    bar = rekap_df[(rekap_df[\"Fakultas\"] == fakultas) & (rekap_df[\"<PERSON><PERSON>\"] == \"Laki-laki\")]\n", "    for p in pendapatan_cols:\n", "        male_counts[p].append(bar[p].values[0] if not bar.empty else 0)\n", "    # Perempuan\n", "    bar = rekap_df[(rekap_df[\"Fakultas\"] == fakultas) & (rekap_df[\"<PERSON><PERSON>\"] == \"Perempuan\")]\n", "    for p in pendapatan_cols:\n", "        female_counts[p].append(bar[p].values[0] if not bar.empty else 0)\n", "\n", "# Susun data secara be<PERSON> [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>la<PERSON>, ...]\n", "bar_data = {p: [] for p in pendapatan_cols}\n", "for i in range(len(fakultas_list)):\n", "    for p in pendapatan_cols:\n", "        bar_data[p].append(male_counts[p][i])\n", "    for p in pendapatan_cols:\n", "        bar_data[p].append(female_counts[p][i])\n", "\n", "x = np.arange(len(labels))\n", "bottom = np.zeros(len(labels))\n", "colors = ['tab:blue', 'tab:orange', 'tab:green', 'tab:red']\n", "\n", "plt.figure(figsize=(18,7))\n", "for i, p in enumerate(pendapatan_cols):\n", "    plt.bar(x, bar_data[p], width=0.6, bottom=bottom, color=colors[i], label=p)\n", "    bottom += np.array(bar_data[p])\n", "\n", "plt.xticks(x, labels, rotation=360)\n", "plt.xlabel(\"Fakultas dan Jen<PERSON>\")\n", "plt.ylabel(\"Jumlah\")\n", "plt.title(\"<PERSON><PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON>' per <PERSON>, <PERSON><PERSON> (L/P), dan <PERSON> (Stacked)\")\n", "plt.legend(title=\"Sumber Pendapatan\")\n", "plt.tight_layout()\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 555}, "id": "kIxB1oNHDSxn", "outputId": "531b51db-4215-4a70-b7e7-7bbd9788824f"}, "execution_count": 22, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1800x700 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["# ==== 1. IMPORT LIBRARY ====\n", "import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.naive_bayes import GaussianNB\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.metrics import classification_report, f1_score\n", "\n", "# ==== 2. LOAD DATASET ====\n", "df = pd.read_csv('dataset_kemacetan_jakarta.csv', delimiter=';')\n", "\n", "# ==== 3. ENCODING FITUR KATEGORIK ====\n", "le_wilayah = LabelEncoder()\n", "le_cuaca = LabelEncoder()\n", "le_jalan = LabelEncoder()\n", "le_kemacetan = LabelEncoder()\n", "\n", "df['wilayah_enc'] = le_wilayah.fit_transform(df['wilayah'])\n", "df['cuaca_enc'] = le_cuaca.fit_transform(df['cuaca'])\n", "df['kondisi_jalan_enc'] = le_jalan.fit_transform(df['kondisi_jalan'])\n", "df['kemacetan_enc'] = le_kemacetan.fit_transform(df['tingkat_kemacetan'])\n", "\n", "# ==== 4. PILIH FITUR DAN TARGET ====\n", "fitur = ['wilayah_enc', 'cuaca_enc', 'kondisi_jalan_enc', 'volume_kendaraan']\n", "X = df[fitur]\n", "y = df['kemacetan_enc']\n", "\n", "# ==== 5. SPLIT DATA TRAIN/TEST ====\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# ==== 6. TRAIN & EVALUASI MODEL ====\n", "# Naive <PERSON>\n", "nb_model = GaussianNB()\n", "nb_model.fit(X_train, y_train)\n", "nb_preds = nb_model.predict(X_test)\n", "\n", "# Decision Tree\n", "dt_model = DecisionTreeClassifier(random_state=42)\n", "dt_model.fit(X_train, y_train)\n", "dt_preds = dt_model.predict(X_test)\n", "\n", "# F1 Score\n", "nb_f1 = f1_score(y_test, nb_preds, average='macro')\n", "dt_f1 = f1_score(y_test, dt_preds, average='macro')\n", "\n", "# Pilih model terbaik\n", "if dt_f1 >= nb_f1:\n", "    best_model = dt_model\n", "    model_name = \"Decision Tree\"\n", "else:\n", "    best_model = nb_model\n", "    model_name = \"<PERSON><PERSON>es\"\n", "\n", "print(f\"\\n>>> Model terbaik berdasarkan F1-score: {model_name}\")\n", "\n", "# ==== 7. EVALUASI LENGKAP ====\n", "print(\"\\n=== Classification Report ===\")\n", "print(classification_report(y_test, best_model.predict(X_test), target_names=le_kemacetan.classes_))\n", "\n", "# ==== 8. PREDIKSI SELURUH DATA ====\n", "df['predicted_kemacetan_enc'] = best_model.predict(X)\n", "df['predicted_kemacetan'] = le_kemacetan.inverse_transform(df['predicted_kemacetan_enc'])\n", "\n", "# ==== 9. REKAP JUMLAH KEMACETAN PER WILAYAH ====\n", "rekap = df.groupby(['wilayah', 'predicted_kemacetan']).size().unstack(fill_value=0)\n", "print(\"\\n=== Rekap Prediksi Kemacetan per Wilayah ===\")\n", "print(rekap)\n", "\n", "# ==== 10. (Opsional) EKSPOR HASIL KE EXCEL ====\n", "df.to_excel('hasil_prediksi_kemacetan.xlsx', index=False)\n", "print(\"\\nFile hasil prediksi telah diekspor ke 'hasil_prediksi_kemacetan.xlsx'\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "CRnHuFOqDmu8", "outputId": "eab0fc62-17ef-4f5a-a05f-492937fd820f"}, "execution_count": 23, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", ">>> Model terb<PERSON><PERSON> F1-score: Decision Tree\n", "\n", "=== Classification Report ===\n", "              precision    recall  f1-score   support\n", "\n", "      rendah       0.67      0.66      0.67       405\n", "      sedang       0.80      0.79      0.80       767\n", "      tinggi       0.91      0.92      0.92       828\n", "\n", "    accuracy                           0.82      2000\n", "   macro avg       0.79      0.79      0.79      2000\n", "weighted avg       0.82      0.82      0.82      2000\n", "\n", "\n", "=== Rekap Prediksi Kemacetan per Wilayah ===\n", "predicted_kemacetan  rendah  sedang  tinggi\n", "wilayah                                    \n", "Cawang                  267     471     510\n", "Kebayoran               283     481     491\n", "Kemang                  258     465     531\n", "Kuningan                251     456     520\n", "Menteng                 250     493     557\n", "Pluit                   247     445     509\n", "Tanjung Priok           255     461     510\n", "<PERSON><PERSON><PERSON>                 254     503     532\n", "\n", "File hasil prediksi telah diekspor ke 'hasil_prediksi_kemacetan.xlsx'\n"]}]}, {"cell_type": "code", "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.naive_bayes import GaussianNB\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.metrics import f1_score\n", "\n", "# --- LOAD & CLEAN DATA ---\n", "df = pd.read_csv('dataset_kemacetan_jakarta.csv', delimiter=';')\n", "df['volume_kendaraan'] = pd.to_numeric(df['volume_kendaraan'], errors='coerce')\n", "df['curah_hujan'] = pd.to_numeric(df['curah_hujan'], errors='coerce')\n", "\n", "for col in ['tingkat_kemacetan', 'kondisi_jalan', 'wilayah']:\n", "    df[col] = df[col].astype(str).str.strip()\n", "\n", "# --- ENCODING FITUR KATEGORIK ---\n", "le_wilayah = LabelEncoder()\n", "le_cuaca = LabelEncoder()\n", "le_jalan = LabelEncoder()\n", "le_kemacetan = LabelEncoder()\n", "\n", "df['wilayah_enc'] = le_wilayah.fit_transform(df['wilayah'])\n", "df['cuaca_enc'] = le_cuaca.fit_transform(df['cuaca'].astype(str).str.strip())\n", "df['kondisi_jalan_enc'] = le_jalan.fit_transform(df['kondisi_jalan'])\n", "df['kemacetan_enc'] = le_kemacetan.fit_transform(df['tingkat_kemacetan'])\n", "\n", "# --- FITUR & TARGET UNTUK MODELLING ---\n", "fitur = ['wilayah_enc', 'cuaca_enc', 'kondisi_jalan_enc', 'volume_kendaraan']\n", "X = df[fitur]\n", "y = df['kemacetan_enc']\n", "\n", "# --- SPLIT TRAIN/TEST ---\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# --- TRAIN & PILIH MODEL TERBAIK ---\n", "nb_model = GaussianNB()\n", "nb_model.fit(X_train, y_train)\n", "nb_preds = nb_model.predict(X_test)\n", "\n", "dt_model = DecisionTreeClassifier(random_state=42)\n", "dt_model.fit(X_train, y_train)\n", "dt_preds = dt_model.predict(X_test)\n", "\n", "nb_f1 = f1_score(y_test, nb_preds, average='macro')\n", "dt_f1 = f1_score(y_test, dt_preds, average='macro')\n", "\n", "if dt_f1 >= nb_f1:\n", "    best_model = dt_model\n", "    model_name = \"Decision Tree\"\n", "else:\n", "    best_model = nb_model\n", "    model_name = \"<PERSON><PERSON>es\"\n", "\n", "print(f\"\\n>>> Model terbaik berdasarkan F1-score: {model_name}\")\n", "\n", "# --- PREDIKSI KESELURUH DATA ---\n", "df['predicted_kemacetan_enc'] = best_model.predict(X)\n", "df['predicted_kemacetan'] = le_kemacetan.inverse_transform(df['predicted_kemacetan_enc'])\n", "\n", "# --- BIN CURAH HUJAN ---\n", "def kbin(ch):\n", "    try:\n", "        ch = float(ch)\n", "    except:\n", "        return np.nan\n", "    if ch <= 5:\n", "        return '0-5'\n", "    elif ch <= 20:\n", "        return '6-20'\n", "    else:\n", "        return '>20'\n", "\n", "df['curah_hujan_bin'] = df['curah_hujan'].apply(kbin)\n", "\n", "# --- AGREGASI TABEL BERDASARKAN PREDIKSI ---\n", "tingkat = sorted(df['predicted_kemacetan'].dropna().unique())\n", "kondisi_jalan = sorted(df['kondisi_jalan'].dropna().unique())\n", "wilayah = sorted(df['wilayah'].dropna().unique())\n", "\n", "columns = []\n", "for t in tingkat:\n", "    for k in ['<PERSON>', 'Mobil']:\n", "        for c in ['0-5', '6-20', '>20']:\n", "            columns.append((t, k, c))\n", "\n", "index = []\n", "for kj in kondisi_jalan:\n", "    index.append(('Kondisi Jalan', kj))\n", "for vk in ['<2500', '2500-4000', '>4000']:\n", "    index.append(('Volume Kendaraan', vk))\n", "for w in wilayah:\n", "    index.append(('Wilayah', w))\n", "\n", "multi_cols = pd.MultiIndex.from_tuples(columns, names=['<PERSON><PERSON><PERSON>', 'Kendaraan', '<PERSON><PERSON><PERSON>'])\n", "multi_idx = pd.MultiIndex.from_tuples(index, names=['<PERSON><PERSON>', '<PERSON><PERSON>'])\n", "out = pd.DataFrame(0, index=multi_idx, columns=multi_cols)\n", "\n", "for (fitur, nilai) in index:\n", "    if fitur == 'Kondisi Jalan':\n", "        df_sel = df[df['kondisi_jalan'] == nilai]\n", "    elif fitur == 'Volume Kendaraan':\n", "        if nilai == '<2500':\n", "            df_sel = df[df['volume_kendaraan'] < 2500]\n", "        elif <PERSON> == '2500-4000':\n", "            df_sel = df[(df['volume_kendaraan'] >= 2500) & (df['volume_kendaraan'] <= 4000)]\n", "        else: # '>4000'\n", "            df_sel = df[df['volume_kendaraan'] > 4000]\n", "    elif fitur == 'Wilayah':\n", "        df_sel = df[df['wilayah'] == nilai]\n", "    else:\n", "        df_sel = df\n", "\n", "    for t in tingkat:\n", "        for c in ['0-5', '6-20', '>20']:\n", "            n = len(df_sel[(df_sel['predicted_kemacetan'] == t) & (df_sel['curah_hujan_bin'] == c)])\n", "            out.loc[(fitur, nilai), (t, 'Motor', c)] = n\n", "            out.loc[(fitur, nilai), (t, '<PERSON><PERSON>', c)] = n\n", "\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', 300)\n", "print(out)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "o8AQ7ymcLffb", "outputId": "0cb84cf7-8e76-4ca3-d60a-57217e66a0e5"}, "execution_count": 30, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", ">>> Model terb<PERSON><PERSON> F1-score: Decision Tree\n", "Tingkat                        rendah                         sedang                         tinggi                          \n", "Kendaraan                       Motor          Mobil           Motor          Mobil           Motor           Mobil          \n", "<PERSON><PERSON><PERSON>                       0-5 6-20 >20   0-5 6-20 >20    0-5 6-20 >20   0-5 6-20 >20    0-5 6-20  >20   0-5 6-20  >20\n", "<PERSON><PERSON>                                                                                                       \n", "Kondisi Jalan    bagus            556  137   0   556  137   0    953  247   0   953  247   0    435  328  632   435  328  632\n", "                 rusak berat      563  124   0   563  124   0   1035  276   0  1035  276   0    434  326  655   434  326  655\n", "                 rusak ringan     548  137   0   548  137   0   1014  250   0  1014  250   0    395  338  617   395  338  617\n", "Volume Kendaraan <2500           1667  398   0  1667  398   0    666  194   0   666  194   0    131  305  784   131  305  784\n", "                 2500-4000          0    0   0     0    0   0   1403  344   0  1403  344   0    103  203  455   103  203  455\n", "                 >4000              0    0   0     0    0   0    933  235   0   933  235   0   1030  484  665  1030  484  665\n", "Wilayah          Cawang           201   66   0   201   66   0    375   96   0   375   96   0    150  118  242   150  118  242\n", "                 Kebayoran        224   59   0   224   59   0    379  102   0   379  102   0    141  116  234   141  116  234\n", "                 Kemang           206   52   0   206   52   0    362  103   0   362  103   0    167  128  236   167  128  236\n", "                 Kuningan         203   48   0   203   48   0    363   93   0   363   93   0    146  122  252   146  122  252\n", "                 Menteng          203   47   0   203   47   0    412   81   0   412   81   0    177  144  236   177  144  236\n", "                 Pluit            204   43   0   204   43   0    358   87   0   358   87   0    150  131  228   150  131  228\n", "                 Tanjung Priok    213   42   0   213   42   0    372   89   0   372   89   0    166  103  241   166  103  241\n", "                 <PERSON><PERSON>rin          213   41   0   213   41   0    381  122   0   381  122   0    167  130  235   167  130  235\n"]}]}, {"cell_type": "code", "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# <PERSON><PERSON> hasil agregasi untuk Wilayah, Motor, curah hujan\n", "out_reset = out.reset_index()\n", "df_plot = out_reset[out_reset['Fitur'] == 'Wilayah']\n", "\n", "# Loop tiap kategori curah hujan, plot bar tiap wilayah\n", "curah_list = ['0-5', '6-20', '>20']\n", "tingkat = out.columns.get_level_values(0).unique().tolist()\n", "\n", "fig, axes = plt.subplots(1, len(curah_list), figsize=(20, 6), sharey=True)\n", "for i, cch in enumerate(curah_list):\n", "    plot_df = pd.DataFrame({\n", "        'Wilayah': df_plot['Ni<PERSON>'],\n", "        **{\n", "            t: df_plot[(t, 'Motor', cch)].values\n", "            for t in tingkat\n", "        }\n", "    })\n", "    plot_df = plot_df.set_index('Wilayah')\n", "    plot_df.plot(kind='bar', ax=axes[i])\n", "    axes[i].set_title(f'<PERSON><PERSON><PERSON>: {cch} mm')\n", "    axes[i].set_ylabel('<PERSON><PERSON><PERSON>diks<PERSON>')\n", "    axes[i].set_xlabel('Wilayah')\n", "    axes[i].legend(title='Ting<PERSON> Ke<PERSON>')\n", "plt.suptitle('Prediksi <PERSON> per Wilayah (Motor, per <PERSON><PERSON><PERSON>)')\n", "plt.tight_layout(rect=[0, 0.03, 1, 0.95])\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 420}, "id": "RTNIKnzvMAIr", "outputId": "b77bc58e-9615-4893-f9a5-ef41de9b7010"}, "execution_count": 31, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 2000x600 with 3 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["for tingkat_kemacetan in ['rendah', 'sedang', 'tinggi']:\n", "    print(f\"\\nKategori: {tingkat_kemacetan}\")\n", "    for kend<PERSON><PERSON> in ['Motor']:\n", "        kemacetan_per_wilayah = out.loc[('Wilayah', slice(None)), (tingkat_kemacetan, kendaraan, slice(None))]\n", "        kemacetan_per_wilayah.columns = kemacetan_per_wilayah.columns.droplevel([0,1])\n", "        kemacetan_per_wilayah['total'] = kemacetan_per_wilayah.sum(axis=1)\n", "        kemacetan_per_wilayah = kemacetan_per_wilayah.reset_index()\n", "        print(kemacetan_per_wilayah[['Nilai', 'total']])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "eAk-EloSNNAN", "outputId": "6467f180-00ef-453a-ac9b-8dda023a1773"}, "execution_count": 35, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Kategori: rendah\n", "<PERSON><PERSON><PERSON>  total\n", "0                   Cawang    267\n", "1                Kebayoran    283\n", "2                   Kemang    258\n", "3                 Kuningan    251\n", "4                  Menteng    250\n", "5                    Pluit    247\n", "6            Tanjung Priok    255\n", "7                  <PERSON><PERSON><PERSON>    254\n", "\n", "Kategori: sedang\n", "<PERSON><PERSON><PERSON>  total\n", "0                   Cawang    471\n", "1                Kebayoran    481\n", "2                   Kemang    465\n", "3                 Kuningan    456\n", "4                  Menteng    493\n", "5                    Pluit    445\n", "6            Tanjung Priok    461\n", "7                  <PERSON><PERSON>rin    503\n", "\n", "Kategori: tinggi\n", "<PERSON><PERSON><PERSON>  total\n", "0                   Cawang    510\n", "1                Kebayoran    491\n", "2                   Kemang    531\n", "3                 Kuningan    520\n", "4                  Menteng    557\n", "5                    Pluit    509\n", "6            Tanjung Priok    510\n", "7                  <PERSON>hamrin    532\n"]}]}, {"cell_type": "code", "source": ["import folium\n", "\n", "# Map warna per tingkat kemacetan\n", "color_map = {'rendah': 'green', 'sedang': 'orange', 'tinggi': 'crimson'}\n", "# Shift sedikit posisi marker agar tidak tumpang-tindih\n", "shift = {'rendah': (0.002, 0), 'sedang': (0, 0.002), 'tinggi': (-0.002, 0)}\n", "\n", "m = folium.Map(location=[-6.2, 106.82], zoom_start=11)\n", "\n", "for tingkat_kemacetan in ['rendah', 'sedang', 'tinggi']:\n", "    for kend<PERSON><PERSON> in ['Motor']:\n", "        kemacetan_per_wilayah = out.loc[('Wilayah', slice(None)), (tingkat_kemacetan, kendaraan, slice(None))]\n", "        kemacetan_per_wilayah.columns = kemacetan_per_wilayah.columns.droplevel([0,1])\n", "        kemacetan_per_wilayah['total'] = kemacetan_per_wilayah.sum(axis=1)\n", "        kemacetan_per_wilayah = kemacetan_per_wilayah.reset_index()\n", "        for idx, row in kemacetan_per_wilayah.iterrows():\n", "            wilayah = row['Nilai']\n", "            total = int(row['total'])\n", "            if total == 0:\n", "                continue\n", "            breakdown = ', '.join([f\"{col}: {int(row[col])}\" for col in ['0-5','6-20','>20']])\n", "            coords = wilayah_coords.get(wilayah, [-6.2, 106.82])\n", "            lat, lon = coords\n", "            dlat, dlon = shift[tingkat_kemacetan]\n", "            folium.CircleMarker(\n", "                location=[lat + dlat, lon + dlon],\n", "                radius=5 + total/30,\n", "                color=color_map[tingkat_kemacetan],\n", "                fill=True,\n", "                fill_opacity=0.7,\n", "                tooltip=f\"{wilayah}\\nKemacetan {tingkat_kemacetan.title()} (Motor): {total}\\nCurah <PERSON>: {breakdown}\"\n", "            ).add_to(m)\n", "\n", "m  # langsung tampil di Jupyter/Colab"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 854}, "id": "btiW5DYHNpPm", "outputId": "8c8dfb1e-9913-43b7-f134-09d114c716cb"}, "execution_count": 36, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<folium.folium.Map at 0x7a2878190290>"], "text/html": ["<div style=\"width:100%;\"><div style=\"position:relative;width:100%;height:0;padding-bottom:60%;\"><span style=\"color:#565656\">Make this Notebook Trusted to load map: File -> Trust Notebook</span><iframe srcdoc=\"&lt;!DOCTYPE html&gt;\n", "&lt;html&gt;\n", "&lt;head&gt;\n", "    \n", "    &lt;meta http-equiv=&quot;content-type&quot; content=&quot;text/html; charset=UTF-8&quot; /&gt;\n", "    &lt;script src=&quot;https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js&quot;&gt;&lt;/script&gt;\n", "    &lt;script src=&quot;https://code.jquery.com/jquery-3.7.1.min.js&quot;&gt;&lt;/script&gt;\n", "    &lt;script src=&quot;https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js&quot;&gt;&lt;/script&gt;\n", "    &lt;script src=&quot;https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js&quot;&gt;&lt;/script&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css&quot;/&gt;\n", "    \n", "            &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width,\n", "                initial-scale=1.0, maximum-scale=1.0, user-scalable=no&quot; /&gt;\n", "            &lt;style&gt;\n", "                #map_f03c7401972de285a73d02b363d5e526 {\n", "                    position: relative;\n", "                    width: 100.0%;\n", "                    height: 100.0%;\n", "                    left: 0.0%;\n", "                    top: 0.0%;\n", "                }\n", "                .leaflet-container { font-size: 1rem; }\n", "            &lt;/style&gt;\n", "\n", "            &lt;style&gt;html, body {\n", "                width: 100%;\n", "                height: 100%;\n", "                margin: 0;\n", "                padding: 0;\n", "            }\n", "            &lt;/style&gt;\n", "\n", "            &lt;style&gt;#map {\n", "                position:absolute;\n", "                top:0;\n", "                bottom:0;\n", "                right:0;\n", "                left:0;\n", "                }\n", "            &lt;/style&gt;\n", "\n", "            &lt;script&gt;\n", "                L_NO_TOUCH = false;\n", "                L_DISABLE_3D = false;\n", "            &lt;/script&gt;\n", "\n", "        \n", "&lt;/head&gt;\n", "&lt;body&gt;\n", "    \n", "    \n", "            &lt;div class=&quot;folium-map&quot; id=&quot;map_f03c7401972de285a73d02b363d5e526&quot; &gt;&lt;/div&gt;\n", "        \n", "&lt;/body&gt;\n", "&lt;script&gt;\n", "    \n", "    \n", "            var map_f03c7401972de285a73d02b363d5e526 = L.map(\n", "                &quot;map_f03c7401972de285a73d02b363d5e526&quot;,\n", "                {\n", "                    center: [-6.2, 106.82],\n", "                    crs: L.CRS.EPSG3857,\n", "                    ...{\n", "  &quot;zoom&quot;: 11,\n", "  &quot;zoomControl&quot;: true,\n", "  &quot;preferCanvas&quot;: false,\n", "}\n", "\n", "                }\n", "            );\n", "\n", "            \n", "\n", "        \n", "    \n", "            var tile_layer_f9060ed2c91d51bedb66d3dda8a7ba13 = L.tileLayer(\n", "                &quot;https://tile.openstreetmap.org/{z}/{x}/{y}.png&quot;,\n", "                {\n", "  &quot;minZoom&quot;: 0,\n", "  &quot;max<PERSON>oom&quot;: 19,\n", "  &quot;maxNative<PERSON>oom&quot;: 19,\n", "  &quot;noWrap&quot;: false,\n", "  &quot;attribution&quot;: &quot;\\u0026copy; \\u003ca href=\\&quot;https://www.openstreetmap.org/copyright\\&quot;\\u003eOpenStreetMap\\u003c/a\\u003e contributors&quot;,\n", "  &quot;subdomains&quot;: &quot;abc&quot;,\n", "  &quot;detectRetina&quot;: false,\n", "  &quot;tms&quot;: false,\n", "  &quot;opacity&quot;: 1,\n", "}\n", "\n", "            );\n", "        \n", "    \n", "            tile_layer_f9060ed2c91d51bedb66d3dda8a7ba13.addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            var circle_marker_f0de773ce27e0bc75056210a9bd6d001 = L.circleMarker(\n", "                [-6.2507, 106.863],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;green&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;green&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 13.9, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_f0de773ce27e0bc75056210a9bd6d001.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Cawang\n", "Ke<PERSON><PERSON><PERSON> (Motor): 267\n", "<PERSON><PERSON><PERSON>: 0-5: 201, 6-20: 66, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_f9bcdf1a1eab7a751c2f990ca28674b1 = <PERSON>.circleMarker(\n", "                [-6.242500000000001, 106.7827],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;green&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;green&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 14.433333333333334, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_f9bcdf1a1eab7a751c2f990ca28674b1.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Ke<PERSON><PERSON>\n", "Ke<PERSON><PERSON><PERSON> (Motor): 283\n", "<PERSON><PERSON><PERSON>: 0-5: 224, 6-20: 59, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_5e267f0f47018233dd971b268a8441a3 = <PERSON>.circle<PERSON>er(\n", "                [-6.264600000000001, 106.8135],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;green&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;green&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 13.6, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_5e267f0f47018233dd971b268a8441a3.bindTooltip(\n", "                `&lt;div&gt;\n", "                     <PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON> (Motor): 258\n", "<PERSON><PERSON><PERSON>: 0-5: 206, 6-20: 52, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_9905f1447e0b81ca518d15d84286f094 = L.circleMarker(\n", "                [-6.2195, 106.8308],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;green&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;green&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 13.366666666666667, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_9905f1447e0b81ca518d15d84286f094.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Kuningan\n", "Ke<PERSON><PERSON><PERSON> (Motor): 251\n", "<PERSON><PERSON><PERSON>: 0-5: 203, 6-20: 48, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_6b0ebab24d5c1a5e936c65b544a4153f = <PERSON>.circleMarker(\n", "                [-6.195600000000001, 106.8498],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;green&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;green&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 13.333333333333334, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_6b0ebab24d5c1a5e936c65b544a4153f.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Menteng\n", "Ke<PERSON><PERSON><PERSON> (Motor): 250\n", "<PERSON><PERSON><PERSON>: 0-5: 203, 6-20: 47, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_55e3528e78c3f786c8e6a0604cab74d9 = <PERSON>.circleMarker(\n", "                [-6.1185, 106.784],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;green&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;green&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 13.233333333333333, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_55e3528e78c3f786c8e6a0604cab74d9.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Pluit\n", "Ke<PERSON><PERSON><PERSON> (Motor): 247\n", "<PERSON><PERSON><PERSON>: 0-5: 204, 6-20: 43, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_ef14dbb70ddc40cc83697c47ca776154 = <PERSON><PERSON>circle<PERSON>(\n", "                [-6.1187000000000005, 106.8867],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;green&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;green&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 13.5, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_ef14dbb70ddc40cc83697c47ca776154.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Tanjung Priok\n", "Ke<PERSON><PERSON><PERSON> (Motor): 255\n", "<PERSON><PERSON><PERSON>: 0-5: 213, 6-20: 42, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_7067500a76735cd44db996bb9422d94b = <PERSON>.circle<PERSON>(\n", "                [-6.1898, 106.8239],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;green&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;green&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 13.466666666666667, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_7067500a76735cd44db996bb9422d94b.bindTooltip(\n", "                `&lt;div&gt;\n", "                     <PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON> (Motor): 254\n", "<PERSON><PERSON><PERSON>: 0-5: 213, 6-20: 41, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_4a27f8695a22d1bd2a76561b02d5855b = <PERSON>.circle<PERSON>arker(\n", "                [-6.2527, 106.865],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;orange&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;orange&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 20.7, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_4a27f8695a22d1bd2a76561b02d5855b.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Cawang\n", "Kemace<PERSON> (Motor): 471\n", "<PERSON><PERSON><PERSON>: 0-5: 375, 6-20: 96, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_191c75d5011afd21e767dc3e8bd59673 = <PERSON><PERSON>circle<PERSON>(\n", "                [-6.2445, 106.7847],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;orange&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;orange&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 21.033333333333335, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_191c75d5011afd21e767dc3e8bd59673.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Ke<PERSON><PERSON>\n", "Kemace<PERSON> (Motor): 481\n", "<PERSON><PERSON><PERSON>: 0-5: 379, 6-20: 102, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_384e5290f248352681d345590b769e63 = <PERSON>.circle<PERSON>arker(\n", "                [-6.2666, 106.8155],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;orange&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;orange&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 20.5, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_384e5290f248352681d345590b769e63.bindTooltip(\n", "                `&lt;div&gt;\n", "                     <PERSON><PERSON><PERSON>\n", "Kemace<PERSON> (Motor): 465\n", "<PERSON><PERSON><PERSON>: 0-5: 362, 6-20: 103, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_ac4af5804358638be156bbd60d273c37 = <PERSON><PERSON>circle<PERSON>(\n", "                [-6.2215, 106.83279999999999],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;orange&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;orange&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 20.2, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_ac4af5804358638be156bbd60d273c37.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Kuningan\n", "Kema<PERSON><PERSON> (Motor): 456\n", "<PERSON><PERSON><PERSON>: 0-5: 363, 6-20: 93, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_ed99f7d60b5c787b04b9629a16c0458e = <PERSON>.circleMarker(\n", "                [-6.1976, 106.8518],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;orange&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;orange&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 21.433333333333334, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_ed99f7d60b5c787b04b9629a16c0458e.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Menteng\n", "Ke<PERSON><PERSON><PERSON> (Motor): 493\n", "<PERSON><PERSON><PERSON>: 0-5: 412, 6-20: 81, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_69648f4007f127a0c1caf5417507b69c = <PERSON>.circle<PERSON>arker(\n", "                [-6.1205, 106.786],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;orange&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;orange&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 19.833333333333336, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_69648f4007f127a0c1caf5417507b69c.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Pluit\n", "Kema<PERSON><PERSON> (Motor): 445\n", "<PERSON><PERSON><PERSON>: 0-5: 358, 6-20: 87, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_b5207bc1bd01784c1bc66413538d7857 = <PERSON>.circleMarker(\n", "                [-6.1207, 106.8887],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;orange&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;orange&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 20.366666666666667, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_b5207bc1bd01784c1bc66413538d7857.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Tanjung Priok\n", "Kemace<PERSON> (Motor): 461\n", "<PERSON><PERSON><PERSON>: 0-5: 372, 6-20: 89, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_7f38e337bee0afd9566cfe05224028c3 = <PERSON><PERSON>circle<PERSON>(\n", "                [-6.1918, 106.82589999999999],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;orange&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;orange&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 21.766666666666666, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_7f38e337bee0afd9566cfe05224028c3.bindTooltip(\n", "                `&lt;div&gt;\n", "                     <PERSON><PERSON><PERSON>\n", "Kemace<PERSON> (Motor): 503\n", "<PERSON><PERSON><PERSON>: 0-5: 381, 6-20: 122, &gt;20: 0\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_7f32a150003f0fc9c7868cf39657a566 = <PERSON>.circle<PERSON>(\n", "                [-6.2547, 106.863],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;crimson&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;crimson&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 22.0, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_7f32a150003f0fc9c7868cf39657a566.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Cawang\n", "<PERSON><PERSON><PERSON><PERSON> (Motor): 510\n", "<PERSON><PERSON><PERSON>: 0-5: 150, 6-20: 118, &gt;20: 242\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_be60cd59951716d679732c96378f646b = <PERSON>.circle<PERSON>arker(\n", "                [-6.2465, 106.7827],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;crimson&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;crimson&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 21.366666666666667, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_be60cd59951716d679732c96378f646b.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Ke<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON> (Motor): 491\n", "<PERSON><PERSON><PERSON>: 0-5: 141, 6-20: 116, &gt;20: 234\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_c5464cea1c2e69301afa4d575cebb08a = <PERSON>.circleMarker(\n", "                [-6.2686, 106.8135],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;crimson&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;crimson&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 22.7, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_c5464cea1c2e69301afa4d575cebb08a.bindTooltip(\n", "                `&lt;div&gt;\n", "                     <PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON> (Motor): 531\n", "<PERSON><PERSON><PERSON>: 0-5: 167, 6-20: 128, &gt;20: 236\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_bb724eac544c6093abcd61eb6dc417cd = <PERSON><PERSON>circle<PERSON>arker(\n", "                [-6.2235, 106.8308],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;crimson&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;crimson&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 22.333333333333332, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_bb724eac544c6093abcd61eb6dc417cd.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Kuningan\n", "<PERSON><PERSON><PERSON><PERSON> (Motor): 520\n", "<PERSON><PERSON><PERSON>: 0-5: 146, 6-20: 122, &gt;20: 252\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_e57e75e33a193a8ccdc41cdc3bfebfcc = <PERSON>.circleMarker(\n", "                [-6.1996, 106.8498],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;crimson&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;crimson&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 23.566666666666666, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_e57e75e33a193a8ccdc41cdc3bfebfcc.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Menteng\n", "Ke<PERSON><PERSON><PERSON> (Motor): 557\n", "<PERSON><PERSON><PERSON>: 0-5: 177, 6-20: 144, &gt;20: 236\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_f7ddb266f4834b05ce2c899dcd2d7a75 = <PERSON>.circleMarker(\n", "                [-6.1225, 106.784],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;crimson&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;crimson&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 21.966666666666665, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_f7ddb266f4834b05ce2c899dcd2d7a75.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Pluit\n", "<PERSON><PERSON><PERSON><PERSON> (Motor): 509\n", "<PERSON><PERSON><PERSON>: 0-5: 150, 6-20: 131, &gt;20: 228\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_9d46c69572fb1e7afe00f024313fb903 = <PERSON>.circle<PERSON>arker(\n", "                [-6.1227, 106.8867],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;crimson&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;crimson&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 22.0, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_9d46c69572fb1e7afe00f024313fb903.bindTooltip(\n", "                `&lt;div&gt;\n", "                     Tanjung Priok\n", "<PERSON><PERSON><PERSON><PERSON> (Motor): 510\n", "<PERSON><PERSON><PERSON>: 0-5: 166, 6-20: 103, &gt;20: 241\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var circle_marker_e4fe2933c6e038a547573893ad674122 = <PERSON><PERSON>circle<PERSON>(\n", "                [-6.1937999999999995, 106.8239],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;crimson&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;crimson&quot;, &quot;fillOpacity&quot;: 0.7, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 22.733333333333334, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_f03c7401972de285a73d02b363d5e526);\n", "        \n", "    \n", "            circle_marker_e4fe2933c6e038a547573893ad674122.bindTooltip(\n", "                `&lt;div&gt;\n", "                     <PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON> (Motor): 532\n", "<PERSON><PERSON><PERSON>: 0-5: 167, 6-20: 130, &gt;20: 235\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "&lt;/script&gt;\n", "&lt;/html&gt;\" style=\"position:absolute;width:100%;height:100%;left:0;top:0;border:none !important;\" allowfullscreen webkitallowfullscreen mozallowfullscreen></iframe></div></div>"]}, "metadata": {}, "execution_count": 36}]}, {"cell_type": "code", "source": [], "metadata": {"id": "BqaOio9YOxHd"}, "execution_count": null, "outputs": []}]}